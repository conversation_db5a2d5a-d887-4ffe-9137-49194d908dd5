/* Dashboard Layout - Black & Gold Theme */
/* Force dark background for entire page when dashboard is active */
body:has(.dashboard-layout-root),
html:has(.dashboard-layout-root) {
  background: var(--color-heritage-black, #181818) !important;
}

.dashboard-layout-root {
  min-height: 100vh;
  background: var(--color-heritage-black, #181818) !important;
  color: var(--color-text-light, #FFF8DC);
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-x: hidden;
  position: relative;
}

.dashboard-layout-grid {
  display: grid;
  grid-template-columns: 320px 1fr 340px;
  gap: 0; /* No gap to completely eliminate white space */
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0; /* No padding */
  background: var(--color-heritage-black, #181818) !important; /* Force dark background */
  min-height: 100vh;
}

.dashboard-sidebar {
  background: rgba(26, 26, 26, 0.95);
  border-radius: 24px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.18);
  padding: 2rem 1.5rem;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  border: none; /* Remove any potential white borders */
}
.dashboard-sidebar--left {
  align-items: flex-start;
}
.dashboard-sidebar--right {
  align-items: flex-end;
  position: sticky;
  top: 0.5rem;
  padding: 0.5rem 0.5rem 0.5rem 0.5rem; /* Minimal padding all around */
  gap: 0.5rem; /* Even more compact gap */
  /* Remove scrolling and max-height for non-scrollable, compact sidebar */
}

/* Profile Section Styles */
.dashboard-profile-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.dashboard-profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.dashboard-profile-avatar {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--color-heritage-gold, #FFD700);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.dashboard-profile-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-text-light, #FFF8DC);
  margin: 0;
}

.dashboard-profile-bio {
  font-size: 0.875rem;
  color: var(--color-heritage-gold, #FFD700);
  margin: 0;
  opacity: 0.9;
}

.dashboard-profile-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 215, 0, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.dashboard-stat {
  text-align: center;
}

.dashboard-stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-heritage-gold, #FFD700);
}

.dashboard-stat-label {
  font-size: 0.75rem;
  color: var(--color-text-light, #FFF8DC);
  opacity: 0.8;
}

.dashboard-profile-detail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: rgba(255, 215, 0, 0.03);
  border-radius: 12px;
  border-left: 3px solid var(--color-heritage-gold, #FFD700);
}

.dashboard-detail-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-heritage-gold, #FFD700);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dashboard-detail-value {
  font-size: 0.875rem;
  color: var(--color-text-light, #FFF8DC);
  line-height: 1.4;
}

.dashboard-skills h4,
.dashboard-communities h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-light, #FFF8DC);
  margin: 0 0 0.75rem 0;
}

.dashboard-skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.dashboard-skill-tag {
  background: rgba(255, 215, 0, 0.15);
  color: var(--color-heritage-gold, #FFD700);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.dashboard-community-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 12px;
  transition: background-color 0.2s ease;
}

.dashboard-community-item:hover {
  background: rgba(255, 215, 0, 0.05);
}

.dashboard-community-icon {
  font-size: 1.5rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-community-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.dashboard-community-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-light, #FFF8DC);
}

.dashboard-community-members {
  font-size: 0.75rem;
  color: var(--color-heritage-gold, #FFD700);
  opacity: 0.8;
}

.dashboard-profile-btn {
  width: 100%;
  background: var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-black, #181818);
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-profile-btn:hover {
  background: var(--color-empowerment-amber, #FFBF00);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* Reduced gap */
  background: var(--color-heritage-black, #181818); /* Force dark background */
  border-radius: 16px; /* Rounded corners to match sidebars */
  padding: 1rem; /* Add some padding */
}

/* Stories Row Styles */
.dashboard-stories-row {
  background: rgba(34, 34, 34, 0.95);
  border-radius: 18px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}

.dashboard-stories-header h4 {
  color: var(--color-text-light, #FFF8DC);
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.dashboard-stories-list {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.dashboard-story-item {
  flex: 0 0 200px;
  background: rgba(255, 215, 0, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-story-item:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.1);
}

.dashboard-story-item:active {
  transform: translateY(0);
}

.dashboard-story-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-heritage-gold, #FFD700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
}

.dashboard-story-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dashboard-story-author {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-light, #FFF8DC);
}

.dashboard-story-topic {
  font-size: 0.75rem;
  color: var(--color-heritage-gold, #FFD700);
  opacity: 0.8;
}

.dashboard-story-preview {
  font-size: 0.75rem;
  color: var(--color-text-light, #FFF8DC);
  opacity: 0.7;
  margin: 0;
  line-height: 1.3;
}

/* Post Input Styles */
.dashboard-post-input {
  background: rgba(34, 34, 34, 0.95);
  border-radius: 18px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}

.dashboard-post-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.dashboard-post-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--color-heritage-gold, #FFD700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.dashboard-post-field {
  flex: 1;
}

.dashboard-post-textarea {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 25px;
  background: rgba(255, 215, 0, 0.05);
  color: var(--color-text-light, #FFF8DC);
  font-size: 1rem;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-post-textarea:hover {
  border-color: var(--color-heritage-gold, #FFD700);
  background: rgba(255, 215, 0, 0.1);
}

.dashboard-post-textarea:focus {
  border-color: var(--color-heritage-gold, #FFD700);
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.dashboard-post-textarea::placeholder {
  color: rgba(255, 248, 220, 0.6);
}

.dashboard-post-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.dashboard-post-btn {
  background: transparent;
  border: 1px solid rgba(255, 215, 0, 0.3);
  color: var(--color-heritage-gold, #FFD700);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-post-btn:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: var(--color-heritage-gold, #FFD700);
}

/* Feed Styles */
.dashboard-feed {
  background: rgba(34, 34, 34, 0.95);
  border-radius: 18px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}

.dashboard-feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-feed-header h4 {
  color: var(--color-text-light, #FFF8DC);
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.dashboard-feed-count {
  font-size: 0.875rem;
  color: var(--color-heritage-gold, #FFD700);
  opacity: 0.8;
}

.dashboard-feed-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--color-text-light, #FFF8DC);
  background: rgba(255, 215, 0, 0.02);
  border-radius: 16px;
  border: 1px dashed rgba(255, 215, 0, 0.3);
}

.dashboard-empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.dashboard-empty-btn {
  background: var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-black, #181818);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
}

.dashboard-empty-btn:hover {
  background: var(--color-empowerment-amber, #FFBF00);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.dashboard-feed-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.dashboard-story-card {
  background: rgba(255, 215, 0, 0.03);
  border: 1px solid rgba(255, 215, 0, 0.15);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-story-card:hover {
  background: rgba(255, 215, 0, 0.05);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(255, 215, 0, 0.1);
}

.dashboard-story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-heritage-gold, #FFD700), var(--color-empowerment-amber, #FFBF00));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-story-card:hover::before {
  opacity: 1;
}

.dashboard-story-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.dashboard-story-author-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dashboard-story-author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-heritage-gold, #FFD700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.dashboard-story-meta {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.dashboard-story-author-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-light, #FFF8DC);
}

.dashboard-story-time {
  font-size: 0.75rem;
  color: var(--color-heritage-gold, #FFD700);
  opacity: 0.7;
}

.dashboard-story-topic-tag {
  background: rgba(255, 215, 0, 0.2);
  color: var(--color-heritage-gold, #FFD700);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.dashboard-story-content {
  margin-bottom: 1rem;
}

.dashboard-story-title {
  color: var(--color-text-light, #FFF8DC);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.dashboard-story-excerpt {
  color: var(--color-text-light, #FFF8DC);
  opacity: 0.8;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 0.75rem 0;
}

.dashboard-story-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.dashboard-story-tag {
  background: rgba(255, 215, 0, 0.1);
  color: var(--color-heritage-gold, #FFD700);
  padding: 0.125rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
  opacity: 0.8;
}

.dashboard-story-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.dashboard-action-btn {
  background: transparent;
  border: none;
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.875rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dashboard-action-btn:hover {
  background: rgba(255, 215, 0, 0.1);
  color: var(--color-heritage-gold, #FFD700);
  transform: scale(1.05);
}

.dashboard-action-btn:active {
  transform: scale(0.95);
}

.dashboard-profile-placeholder,
.dashboard-activity-placeholder {
  color: var(--color-heritage-gold, #FFD700);
  font-size: 1.1rem;
  text-align: center;
  opacity: 0.8;
}

/* Activity Sidebar Styles */
.dashboard-activity-section {
  margin-bottom: 2rem;
}

.dashboard-activity-header {
  color: var(--color-text-light, #FFF8DC);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-activity-count {
  background: var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-black, #181818);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
}

/* Quick Actions */
.dashboard-quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.dashboard-quick-action {
  background: rgba(255, 215, 0, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.dashboard-quick-action:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: var(--color-heritage-gold, #FFD700);
  transform: translateY(-1px);
}

.dashboard-quick-icon {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-heritage-gold, #FFD700);
  border-radius: 50%;
}

.dashboard-quick-label {
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Notifications */
.dashboard-notifications {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.dashboard-notification {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 215, 0, 0.03);
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.dashboard-notification:hover {
  background: rgba(255, 215, 0, 0.05);
  border-color: rgba(255, 215, 0, 0.2);
}

.dashboard-notification.unread {
  background: rgba(255, 215, 0, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.dashboard-notification-icon {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.dashboard-notification-content {
  flex: 1;
}

.dashboard-notification-message {
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.875rem;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.dashboard-notification-time {
  color: var(--color-heritage-gold, #FFD700);
  font-size: 0.75rem;
  opacity: 0.8;
}

.dashboard-notification-dot {
  width: 8px;
  height: 8px;
  background: var(--color-heritage-gold, #FFD700);
  border-radius: 50%;
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

/* Recent Activity */
.dashboard-recent-activity {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.dashboard-activity-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 215, 0, 0.03);
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-activity-item:hover {
  background: rgba(255, 215, 0, 0.05);
  border-color: rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.dashboard-activity-icon {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 215, 0, 0.2);
  border-radius: 50%;
  flex-shrink: 0;
}

.dashboard-activity-content {
  flex: 1;
}

.dashboard-activity-title {
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.875rem;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.dashboard-activity-time {
  color: var(--color-heritage-gold, #FFD700);
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Suggested Connections */
.dashboard-suggested-connections {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dashboard-connection-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 215, 0, 0.03);
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.dashboard-connection-item:hover {
  background: rgba(255, 215, 0, 0.05);
  border-color: rgba(255, 215, 0, 0.2);
}

.dashboard-connection-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-heritage-gold, #FFD700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.dashboard-connection-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.dashboard-connection-name {
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.875rem;
  font-weight: 600;
}

.dashboard-connection-role {
  color: var(--color-heritage-gold, #FFD700);
  font-size: 0.75rem;
  opacity: 0.8;
}

.dashboard-connection-mutual {
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.75rem;
  opacity: 0.6;
}

.dashboard-connection-btn {
  background: var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-black, #181818);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.dashboard-connection-btn:hover {
  background: var(--color-empowerment-amber, #FFBF00);
  transform: translateY(-1px);
}

/* Community Stats */
.dashboard-community-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dashboard-stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 215, 0, 0.03);
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 12px;
}

.dashboard-stat-icon {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 215, 0, 0.2);
  border-radius: 50%;
  flex-shrink: 0;
}

.dashboard-stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.dashboard-stat-number {
  color: var(--color-heritage-gold, #FFD700);
  font-size: 1rem;
  font-weight: 700;
}

.dashboard-stat-label {
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.75rem;
  opacity: 0.8;
}

/* View All Button */
.dashboard-view-all-btn {
  background: transparent;
  border: 1px solid rgba(255, 215, 0, 0.3);
  color: var(--color-heritage-gold, #FFD700);
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.dashboard-view-all-btn:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: var(--color-heritage-gold, #FFD700);
}

/* Trending Topics */
.dashboard-trending-topics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.dashboard-trending-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 215, 0, 0.03);
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-trending-item:hover {
  background: rgba(255, 215, 0, 0.05);
  border-color: rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.dashboard-trending-tag {
  color: var(--color-heritage-gold, #FFD700);
  font-size: 0.875rem;
  font-weight: 600;
}

.dashboard-trending-count {
  color: var(--color-text-light, #FFF8DC);
  font-size: 0.75rem;
  background: rgba(255, 215, 0, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  opacity: 0.8;
}

/* Enhanced animations and micro-interactions */
.dashboard-notification:hover .dashboard-notification-icon {
  transform: scale(1.1);
}

.dashboard-activity-item:hover .dashboard-activity-icon {
  transform: scale(1.1);
  background: rgba(255, 215, 0, 0.3);
}

.dashboard-stat-item:hover .dashboard-stat-icon {
  transform: scale(1.1);
  background: rgba(255, 215, 0, 0.3);
}

.dashboard-trending-item:hover .dashboard-trending-count {
  background: var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-black, #181818);
}

/* Loading states */
.dashboard-activity-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--color-heritage-gold, #FFD700);
}

.dashboard-activity-loading::after {
  content: "⏳";
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* Smooth transitions for all interactive elements */
.dashboard-quick-action,
.dashboard-notification,
.dashboard-activity-item,
.dashboard-connection-item,
.dashboard-stat-item,
.dashboard-trending-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus states for accessibility */
.dashboard-quick-action:focus,
.dashboard-notification:focus,
.dashboard-activity-item:focus,
.dashboard-connection-item:focus,
.dashboard-trending-item:focus {
  outline: 2px solid var(--color-heritage-gold, #FFD700);
  outline-offset: 2px;
}

/* Badge animations */
.dashboard-activity-count {
  animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* Responsive improvements for activity sidebar */
@media (max-width: 1400px) {
  .dashboard-activity-section {
    margin-bottom: 1.5rem;
  }
  
  .dashboard-quick-actions {
    gap: 0.5rem;
  }
  
  .dashboard-quick-action {
    padding: 0.75rem;
  }
}

@media (max-width: 1200px) {
  .dashboard-activity-section {
    margin-bottom: 1rem;
  }
  
  .dashboard-activity-header {
    font-size: 0.875rem;
  }
  
  .dashboard-connection-item {
    padding: 0.75rem;
  }
}

@media (max-width: 1200px) {
  .dashboard-layout-grid {
    grid-template-columns: 240px 1fr 260px;
    gap: 1rem;
  }
}
@media (max-width: 900px) {
  .dashboard-layout-grid {
    grid-template-columns: 1fr;
  }
  .dashboard-sidebar--left,
  .dashboard-sidebar--right,
  .naroop-activity-sidebar {
    display: none;
  }
}

/* ===== NAROOP ACTIVITY SIDEBAR - MODERN COMPACT DESIGN ===== */

/* Main Sidebar Container - Dark Theme to Match Site */
.naroop-activity-sidebar {
  background: rgba(26, 26, 26, 0.95); /* Heritage black to match existing dashboard */
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
  padding: 1rem;
  min-height: fit-content;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: sticky;
  top: 1rem;
  width: 100%;
  max-width: 320px;
  border: 1px solid rgba(255, 215, 0, 0.1); /* Heritage gold border */
}

/* Section Containers */
.naroop-activity-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.naroop-activity-section:not(:last-child) {
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 215, 0, 0.1); /* Heritage gold border */
}

/* Section Titles */
.naroop-section-title {
  color: var(--color-text-light, #FFF8DC); /* Heritage cream text for dark theme */
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 1.2;
}

/* Quick Actions */
.naroop-quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.naroop-quick-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 50px; /* Pill-shaped */
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  background: transparent;
  min-height: 36px;
  text-align: left;
}

.naroop-btn-icon {
  font-size: 0.875rem;
  flex-shrink: 0;
}

/* Primary Quick Action Button */
.naroop-btn-primary {
  border-color: var(--color-heritage-gold, #FFD700); /* Heritage gold */
  color: var(--color-heritage-gold, #FFD700);
}

.naroop-btn-primary:hover {
  background: var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-black, #1A1A1A);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

/* Secondary Quick Action Button */
.naroop-btn-secondary {
  border-color: var(--color-heritage-forest, #355E3B); /* Heritage forest green */
  color: var(--color-heritage-forest, #355E3B);
}

.naroop-btn-secondary:hover {
  background: var(--color-heritage-forest, #355E3B);
  color: var(--color-text-light, #FFF8DC);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(53, 94, 59, 0.3);
}

/* Accent Quick Action Button */
.naroop-btn-accent {
  border-color: var(--color-empowerment-amber, #FFBF00); /* Empowerment amber */
  color: var(--color-empowerment-amber, #FFBF00);
}

.naroop-btn-accent:hover {
  background: var(--color-empowerment-amber, #FFBF00);
  color: var(--color-heritage-black, #1A1A1A);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 191, 0, 0.3);
}

/* Notification Badge */
.naroop-notification-badge {
  background: var(--color-heritage-gold, #FFD700); /* Heritage gold */
  color: var(--color-heritage-black, #1A1A1A);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
}

/* Notifications List */
.naroop-notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.naroop-notification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 215, 0, 0.03); /* Heritage gold tint */
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.naroop-notification-item:hover {
  background: rgba(255, 215, 0, 0.05);
  border-color: rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.naroop-notification-item.unread {
  background: rgba(255, 215, 0, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.naroop-notification-icon {
  font-size: 1rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 215, 0, 0.2); /* Heritage gold background */
  border-radius: 50%;
  flex-shrink: 0;
}

.naroop-notification-content {
  flex: 1;
  min-width: 0;
}

.naroop-notification-text {
  color: var(--color-text-light, #FFF8DC); /* Heritage cream text */
  font-size: 0.8rem;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.naroop-notification-time {
  color: var(--color-heritage-gold, #FFD700); /* Heritage gold for time */
  font-size: 0.7rem;
  opacity: 0.8;
}

.naroop-unread-dot {
  width: 6px;
  height: 6px;
  background: var(--color-heritage-gold, #FFD700);
  border-radius: 50%;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  flex-shrink: 0;
}

/* Activity List */
.naroop-activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.naroop-activity-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 191, 0, 0.03); /* Empowerment amber tint */
  border: 1px solid rgba(255, 191, 0, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.naroop-activity-item:hover {
  background: rgba(255, 191, 0, 0.05);
  border-color: rgba(255, 191, 0, 0.2);
  transform: translateY(-1px);
}

.naroop-activity-icon {
  font-size: 1rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 191, 0, 0.2); /* Empowerment amber background */
  border-radius: 50%;
  flex-shrink: 0;
}

.naroop-activity-content {
  flex: 1;
  min-width: 0;
}

.naroop-activity-text {
  color: var(--color-text-light, #FFF8DC); /* Heritage cream text */
  font-size: 0.8rem;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.naroop-activity-time {
  color: var(--color-empowerment-amber, #FFBF00); /* Empowerment amber for time */
  font-size: 0.7rem;
  opacity: 0.8;
}

/* Connections List */
.naroop-connections-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.naroop-connection-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(205, 127, 50, 0.03); /* Community bronze tint */
  border: 1px solid rgba(205, 127, 50, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.naroop-connection-item:hover {
  background: rgba(205, 127, 50, 0.05);
  border-color: rgba(205, 127, 50, 0.2);
  transform: translateY(-1px);
}

.naroop-connection-avatar {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(205, 127, 50, 0.2); /* Community bronze background */
  border-radius: 50%;
  flex-shrink: 0;
}

.naroop-connection-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.naroop-connection-name {
  color: var(--color-text-light, #FFF8DC); /* Heritage cream text */
  font-size: 0.8rem;
  font-weight: 600;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.naroop-connection-role {
  color: var(--color-community-bronze, #CD7F32); /* Community bronze */
  font-size: 0.7rem;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.naroop-connection-mutual {
  color: var(--color-community-bronze, #CD7F32); /* Community bronze */
  font-size: 0.65rem;
  opacity: 0.8;
  margin: 0;
}

.naroop-connect-btn {
  background: transparent;
  border: 1px solid var(--color-heritage-forest, #355E3B);
  color: var(--color-heritage-forest, #355E3B);
  padding: 0.25rem 0.5rem;
  border-radius: 50px;
  font-size: 0.7rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.naroop-connect-btn:hover {
  background: var(--color-heritage-forest, #355E3B);
  color: var(--color-text-light, #FFF8DC);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(53, 94, 59, 0.3);
}

/* View All Button */
.naroop-view-all-btn {
  background: transparent;
  border: 1px solid var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-gold, #FFD700);
  padding: 0.5rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  margin-top: 0.25rem;
}

.naroop-view-all-btn:hover {
  background: var(--color-heritage-gold, #FFD700);
  color: var(--color-heritage-black, #1A1A1A);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3);
}

/* Responsive Design for NAROOP Activity Sidebar */
@media (max-width: 1400px) {
  .naroop-activity-sidebar {
    max-width: 280px;
    padding: 0.875rem;
    gap: 0.875rem;
  }

  .naroop-activity-section {
    gap: 0.625rem;
  }

  .naroop-section-title {
    font-size: 0.8rem;
  }

  .naroop-quick-btn {
    padding: 0.4rem 0.625rem;
    font-size: 0.75rem;
    min-height: 32px;
  }
}

@media (max-width: 1200px) {
  .naroop-activity-sidebar {
    max-width: 260px;
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .naroop-activity-section {
    gap: 0.5rem;
  }

  .naroop-section-title {
    font-size: 0.75rem;
  }

  .naroop-notification-text,
  .naroop-activity-text {
    font-size: 0.75rem;
  }

  .naroop-notification-time,
  .naroop-activity-time {
    font-size: 0.65rem;
  }
}

@media (max-width: 900px) {
  .naroop-activity-sidebar {
    display: none;
  }
}

/* Focus States for Accessibility */
.naroop-quick-btn:focus,
.naroop-notification-item:focus,
.naroop-activity-item:focus,
.naroop-connection-item:focus,
.naroop-connect-btn:focus,
.naroop-view-all-btn:focus {
  outline: 2px solid #F7D046;
  outline-offset: 2px;
}

/* Smooth Scrolling for Sidebar */
.naroop-activity-sidebar {
  scroll-behavior: smooth;
}

.naroop-activity-sidebar::-webkit-scrollbar {
  width: 4px;
}

.naroop-activity-sidebar::-webkit-scrollbar-track {
  background: rgba(255, 215, 0, 0.1); /* Heritage gold track */
  border-radius: 2px;
}

.naroop-activity-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.3); /* Heritage gold thumb */
  border-radius: 2px;
}

.naroop-activity-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.5); /* Heritage gold hover */
}

/* Animation for Interactive Elements */
.naroop-notification-item:hover .naroop-notification-icon {
  transform: scale(1.05);
  background: rgba(255, 215, 0, 0.3); /* Heritage gold hover */
}

.naroop-activity-item:hover .naroop-activity-icon {
  transform: scale(1.05);
  background: rgba(255, 191, 0, 0.3); /* Empowerment amber hover */
}

.naroop-connection-item:hover .naroop-connection-avatar {
  transform: scale(1.05);
  background: rgba(205, 127, 50, 0.3); /* Community bronze hover */
}

/* Loading State */
.naroop-activity-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: var(--color-heritage-gold, #FFD700);
  font-size: 0.875rem;
}

.naroop-activity-loading::after {
  content: "⏳";
  animation: pulse 1.5s infinite;
  margin-left: 0.5rem;
}

/* Empty State */
.naroop-activity-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  text-align: center;
  color: var(--color-heritage-gold, #FFD700);
  opacity: 0.8;
}

.naroop-activity-empty-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.naroop-activity-empty-text {
  font-size: 0.8rem;
  margin: 0;
}

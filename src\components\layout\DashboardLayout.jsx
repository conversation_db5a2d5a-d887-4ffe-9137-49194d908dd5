import React from "react";
import "./DashboardLayout.css";

// Stories row component for featured stories/highlights
const StoriesRow = ({ featuredStories = [], userProfiles = {}, onStoryClick }) => {
  // Sample featured stories if none provided
  const defaultStories = [
    { id: 1, author: '<PERSON>', avatar: '👩🏾‍💼', topic: 'Business', preview: 'Building my dream...' },
    { id: 2, author: '<PERSON>', avatar: '👨🏿‍🎨', topic: 'Art', preview: 'Creating change through...' },
    { id: 3, author: '<PERSON>', avatar: '👩🏾‍🎓', topic: 'Education', preview: 'Teaching the next...' },
    { id: 4, author: 'Your Story', avatar: '+', topic: 'Share', preview: 'Add your voice...' }
  ];

  const stories = featuredStories.length > 0 ? featuredStories.slice(0, 4) : defaultStories;

  const handleStoryClick = (story) => {
    if (story.id === 4 || story.author === 'Your Story') {
      // Handle "Add your story" click
      if (onStoryClick) {
        onStoryClick('create');
      }
    } else {
      // Handle story view click
      if (onStoryClick) {
        onStoryClick('view', story);
      }
    }
  };

  return (
    <div className="dashboard-stories-row">
      <div className="dashboard-stories-header">
        <h4>✨ Featured Stories</h4>
      </div>
      <div className="dashboard-stories-list">
        {stories.map(story => (
          <div 
            key={story.id} 
            className="dashboard-story-item"
            onClick={() => handleStoryClick(story)}
          >
            <div className="dashboard-story-avatar">
              {story.avatar || userProfiles[story.author]?.avatar || '👤'}
            </div>
            <div className="dashboard-story-info">
              <span className="dashboard-story-author">
                {story.author || userProfiles[story.author]?.name || 'Community Member'}
              </span>
              <span className="dashboard-story-topic">{story.topic}</span>
              <p className="dashboard-story-preview">
                {story.preview || (story.content && story.content.substring(0, 40) + '...')}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Post input component for creating new stories
const PostInput = ({ onCreatePost, user }) => {
  const handleCreatePost = () => {
    // Navigate to story creation or open modal
    if (onCreatePost) {
      onCreatePost();
    }
  };

  const handleMediaAction = (type) => {
    // Handle different media types
    console.log(`${type} action clicked`);
    handleCreatePost();
  };

  return (
    <div className="dashboard-post-input">
      <div className="dashboard-post-header">
        <div className="dashboard-post-avatar">{user?.avatar || '👤'}</div>
        <div className="dashboard-post-field">
          <input 
            type="text" 
            placeholder={`What's your story, ${user?.name || 'friend'}?`}
            className="dashboard-post-textarea"
            onClick={handleCreatePost}
            onFocus={handleCreatePost}
            readOnly
          />
        </div>
      </div>
      <div className="dashboard-post-actions">
        <button 
          className="dashboard-post-btn dashboard-post-btn--photo"
          onClick={() => handleMediaAction('photo')}
        >
          📷 Photo
        </button>
        <button 
          className="dashboard-post-btn dashboard-post-btn--video"
          onClick={() => handleMediaAction('video')}
        >
          🎥 Video
        </button>
        <button 
          className="dashboard-post-btn dashboard-post-btn--poll"
          onClick={() => handleMediaAction('poll')}
        >
          📊 Poll
        </button>
        <button 
          className="dashboard-post-btn dashboard-post-btn--schedule"
          onClick={() => handleMediaAction('schedule')}
        >
          📅 Schedule
        </button>
      </div>
    </div>
  );
};

// Feed component for displaying stories
const Feed = ({ stories = [], userProfiles = {}, onReaction, onShare, onBookmark, user, onCreatePost }) => {
  const recentStories = stories.slice(0, 5); // Show latest 5 stories

  const handleEmptyAction = () => {
    if (onCreatePost) {
      onCreatePost();
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Just now';
    
    // Handle different timestamp formats
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return timestamp;
    
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  };

  return (
    <div className="dashboard-feed">
      <div className="dashboard-feed-header">
        <h4>Recent Stories</h4>
        <span className="dashboard-feed-count">{stories.length} stories</span>
      </div>
      
      {recentStories.length === 0 ? (
        <div className="dashboard-feed-empty">
          <div className="dashboard-empty-icon">📝</div>
          <p>No stories yet. Be the first to share your experience!</p>
          <button className="dashboard-empty-btn" onClick={handleEmptyAction}>
            Share Your Story
          </button>
        </div>
      ) : (
        <div className="dashboard-feed-list">
          {recentStories.map(story => (
            <article key={story.id} className="dashboard-story-card">
              <div className="dashboard-story-header">
                <div className="dashboard-story-author-info">
                  <div className="dashboard-story-author-avatar">
                    {userProfiles[story.author]?.avatar || '👤'}
                  </div>
                  <div className="dashboard-story-meta">
                    <span className="dashboard-story-author-name">
                      {userProfiles[story.author]?.name || 'Community Member'}
                    </span>
                    <span className="dashboard-story-time">
                      {formatTimestamp(story.timestamp)}
                    </span>
                  </div>
                </div>
                <span className="dashboard-story-topic-tag">{story.topic}</span>
              </div>

              <div className="dashboard-story-content">
                <h5 className="dashboard-story-title">{story.title}</h5>
                <p className="dashboard-story-excerpt">
                  {story.content?.substring(0, 200)}
                  {story.content?.length > 200 ? '...' : ''}
                </p>
                {story.tags && story.tags.length > 0 && (
                  <div className="dashboard-story-tags">
                    {story.tags.slice(0, 3).map(tag => (
                      <span key={tag} className="dashboard-story-tag">#{tag}</span>
                    ))}
                  </div>
                )}
              </div>

              <div className="dashboard-story-actions">
                <button 
                  className="dashboard-action-btn dashboard-action-btn--heart"
                  onClick={() => onReaction && onReaction(story.id, 'heart')}
                  title="Show love for this story"
                >
                  ❤️ {story.hearts || 0}
                </button>
                <button 
                  className="dashboard-action-btn dashboard-action-btn--clap"
                  onClick={() => onReaction && onReaction(story.id, 'clap')}
                  title="Applaud this story"
                >
                  👏 {story.claps || 0}
                </button>
                <button 
                  className="dashboard-action-btn dashboard-action-btn--share"
                  onClick={() => onShare && onShare(story)}
                  title="Share this story"
                >
                  📤 {story.shares || 0}
                </button>
                <button 
                  className="dashboard-action-btn dashboard-action-btn--bookmark"
                  onClick={() => onBookmark && onBookmark(story.id)}
                  title="Bookmark this story"
                >
                  📚
                </button>
              </div>
            </article>
          ))}
        </div>
      )}
    </div>
  );
};
const ProfileSidebar = ({ user, userStories, onProfileUpdate, onShowFullProfile }) => {
  const totalReactions = userStories?.reduce((sum, story) => sum + (story.hearts || 0) + (story.claps || 0), 0) || 0;
  
  return (
    <aside className="dashboard-sidebar dashboard-sidebar--left" aria-label="Profile and Skills">
      <div className="dashboard-profile-section">
        {/* Profile Header */}
        <div className="dashboard-profile-header">
          <div className="dashboard-profile-avatar" aria-label="User avatar">
            {user?.avatar || '👤'}
          </div>
          <div className="dashboard-profile-info">
            <h3 className="dashboard-profile-name">{user?.name || user?.email || 'User'}</h3>
            <p className="dashboard-profile-bio">{user?.bio || 'Welcome to NAROOP!'}</p>
          </div>
        </div>

        {/* Profile Stats */}
        <div className="dashboard-profile-stats">
          <div className="dashboard-stat">
            <div className="dashboard-stat-number">{userStories?.length || 0}</div>
            <div className="dashboard-stat-label">Stories</div>
          </div>
          <div className="dashboard-stat">
            <div className="dashboard-stat-number">{totalReactions}</div>
            <div className="dashboard-stat-label">Hearts</div>
          </div>
          <div className="dashboard-stat">
            <div className="dashboard-stat-number">{user?.badges?.length || 0}</div>
            <div className="dashboard-stat-label">Badges</div>
          </div>
        </div>

        {/* Profile Details */}
        {user?.roleModel && (
          <div className="dashboard-profile-detail">
            <span className="dashboard-detail-label">Role Model:</span>
            <span className="dashboard-detail-value">{user.roleModel}</span>
          </div>
        )}
        
        {user?.tradition && (
          <div className="dashboard-profile-detail">
            <span className="dashboard-detail-label">Values:</span>
            <span className="dashboard-detail-value">{user.tradition}</span>
          </div>
        )}

        {user?.dream && (
          <div className="dashboard-profile-detail">
            <span className="dashboard-detail-label">Dreams:</span>
            <span className="dashboard-detail-value">{user.dream}</span>
          </div>
        )}

        {/* Skills Section */}
        <div className="dashboard-skills">
          <h4>Skills</h4>
          <div className="dashboard-skills-list">
            <span className="dashboard-skill-tag">Storytelling</span>
            <span className="dashboard-skill-tag">Community</span>
            <span className="dashboard-skill-tag">Leadership</span>
            <span className="dashboard-skill-tag">Inspiration</span>
          </div>
        </div>

        {/* Communities Section */}
        <div className="dashboard-communities">
          <h4>Communities</h4>
          <div className="dashboard-community-item">
            <div className="dashboard-community-icon">✊🏾</div>
            <div className="dashboard-community-info">
              <span className="dashboard-community-name">Black Excellence</span>
              <span className="dashboard-community-members">1,984 members</span>
            </div>
          </div>
          <div className="dashboard-community-item">
            <div className="dashboard-community-icon">🎨</div>
            <div className="dashboard-community-info">
              <span className="dashboard-community-name">Creative Arts</span>
              <span className="dashboard-community-members">847 members</span>
            </div>
          </div>
          <div className="dashboard-community-item">
            <div className="dashboard-community-icon">💼</div>
            <div className="dashboard-community-info">
              <span className="dashboard-community-name">Entrepreneurs</span>
              <span className="dashboard-community-members">1,203 members</span>
            </div>
          </div>
        </div>

        {/* Profile Action */}
        <div className="dashboard-profile-action">
          <button 
            className="dashboard-profile-btn"
            onClick={onShowFullProfile}
          >
            My Profile
          </button>
        </div>
      </div>
    </aside>
  );
};

const RecentActivitySidebar = ({ 
  notifications = [], 
  recentActivity = [], 
  suggestedConnections = [], 
  onNotificationClick,
  onActivityClick,
  onConnectionClick,
  onQuickAction,
  user 
}) => {
  // Sample data if none provided
  const defaultNotifications = [
    { id: 1, type: 'heart', message: 'Maya loved your story "My Journey"', time: '2h ago', unread: true },
    { id: 2, type: 'comment', message: 'New comment on "Community Building"', time: '4h ago', unread: true },
    { id: 3, type: 'milestone', message: 'You reached 50 hearts! 🎉', time: '1d ago', unread: false }
  ];

  const defaultActivity = [
    { id: 1, type: 'story', action: 'published', title: 'New story from Marcus', time: '1h ago', author: 'Marcus Williams' },
    { id: 2, type: 'milestone', action: 'achieved', title: 'Alicia earned "Storyteller" badge', time: '3h ago', author: 'Alicia Davis' },
    { id: 3, type: 'community', action: 'joined', title: 'Sarah joined Creative Arts', time: '5h ago', author: 'Sarah Johnson' }
  ];

  const defaultConnections = [
    { id: 1, name: 'Dr. Angela Brown', role: 'Educator', mutual: 3, avatar: '👩🏾‍🏫' },
    { id: 2, name: 'James Wilson', role: 'Entrepreneur', mutual: 7, avatar: '👨🏿‍💼' },
    { id: 3, name: 'Lisa Thompson', role: 'Artist', mutual: 2, avatar: '👩🏽‍🎨' }
  ];

  const currentNotifications = notifications.length > 0 ? notifications : defaultNotifications;
  const currentActivity = recentActivity.length > 0 ? recentActivity : defaultActivity;

  // Limit items for compactness
  const MAX_NOTIFICATIONS = 2;
  const MAX_ACTIVITY = 2;
  const MAX_CONNECTIONS = 1;
  const currentConnections = suggestedConnections.length > 0 ? suggestedConnections : defaultConnections;

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'heart': return '❤️';
      case 'comment': return '💬';
      case 'milestone': return '🎯';
      case 'story': return '📖';
      default: return '🔔';
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'story': return '📖';
      case 'milestone': return '🏆';
      case 'community': return '👥';
      default: return '✨';
    }
  };

  const handleQuickAction = (action) => {
    if (onQuickAction) {
      onQuickAction(action);
    }
  };

  return (
    <aside className="dashboard-sidebar dashboard-sidebar--right" aria-label="Recent Activity" style={{ minWidth: 0 }}>
      {/* Quick Actions */}
      <div className="dashboard-activity-section">
        <h4 className="dashboard-activity-header">Quick Actions</h4>
        <div className="dashboard-quick-actions">
          <button 
            className="dashboard-quick-action"
            onClick={() => handleQuickAction('create-story')}
            title="Create new story"
          >
            <span className="dashboard-quick-icon">✏️</span>
            <span className="dashboard-quick-label">Write Story</span>
          </button>
          <button 
            className="dashboard-quick-action"
            onClick={() => handleQuickAction('explore')}
            title="Explore community"
          >
            <span className="dashboard-quick-icon">🔍</span>
            <span className="dashboard-quick-label">Explore</span>
          </button>
          <button 
            className="dashboard-quick-action"
            onClick={() => handleQuickAction('connect')}
            title="Find connections"
          >
            <span className="dashboard-quick-icon">🤝</span>
            <span className="dashboard-quick-label">Connect</span>
          </button>
        </div>
      </div>

      {/* Notifications */}
      <div className="dashboard-activity-section">
        <h4 className="dashboard-activity-header">
          Notifications
          <span className="dashboard-activity-count">
            {currentNotifications.filter(n => n.unread).length}
          </span>
        </h4>
        <div className="dashboard-notifications">
          {currentNotifications.slice(0, MAX_NOTIFICATIONS).map(notification => (
            <div 
              key={notification.id}
              className={`dashboard-notification ${notification.unread ? 'unread' : ''}`}
              onClick={() => onNotificationClick && onNotificationClick(notification)}
              style={{ minHeight: 0, padding: '4px 0' }}
            >
              <div className="dashboard-notification-icon">
                {getNotificationIcon(notification.type)}
              </div>
              <div className="dashboard-notification-content">
                <p className="dashboard-notification-message" style={{ fontSize: '0.95em', margin: 0 }}>{notification.message}</p>
                <span className="dashboard-notification-time" style={{ fontSize: '0.85em' }}>{notification.time}</span>
              </div>
              {notification.unread && <div className="dashboard-notification-dot"></div>}
            </div>
          ))}
        </div>
        {currentNotifications.length > MAX_NOTIFICATIONS && (
          <button className="dashboard-view-all-btn">View All</button>
        )}
      </div>

      {/* Recent Activity */}
      <div className="dashboard-activity-section">
        <h4 className="dashboard-activity-header">Recent Activity</h4>
        <div className="dashboard-recent-activity">
          {currentActivity.slice(0, MAX_ACTIVITY).map(activity => (
            <div 
              key={activity.id}
              className="dashboard-activity-item"
              onClick={() => onActivityClick && onActivityClick(activity)}
              style={{ minHeight: 0, padding: '4px 0' }}
            >
              <div className="dashboard-activity-icon">
                {getActivityIcon(activity.type)}
              </div>
              <div className="dashboard-activity-content">
                <p className="dashboard-activity-title" style={{ fontSize: '0.95em', margin: 0 }}>{activity.title}</p>
                <span className="dashboard-activity-time" style={{ fontSize: '0.85em' }}>{activity.time}</span>
              </div>
            </div>
          ))}
        </div>
        {currentActivity.length > MAX_ACTIVITY && (
          <button className="dashboard-view-all-btn">View All</button>
        )}
      </div>

      {/* Suggested Connections */}
      <div className="dashboard-activity-section">
        <h4 className="dashboard-activity-header">People You May Know</h4>
        <div className="dashboard-suggested-connections">
          {currentConnections.slice(0, MAX_CONNECTIONS).map(connection => (
            <div key={connection.id} className="dashboard-connection-item" style={{ minHeight: 0, padding: '4px 0' }}>
              <div className="dashboard-connection-avatar">{connection.avatar}</div>
              <div className="dashboard-connection-info">
                <span className="dashboard-connection-name" style={{ fontSize: '0.95em' }}>{connection.name}</span>
                <span className="dashboard-connection-role" style={{ fontSize: '0.85em' }}>{connection.role}</span>
                <span className="dashboard-connection-mutual" style={{ fontSize: '0.8em' }}>{connection.mutual} mutual</span>
              </div>
              <button 
                className="dashboard-connection-btn"
                onClick={() => onConnectionClick && onConnectionClick(connection)}
                style={{ fontSize: '0.9em', padding: '2px 8px' }}
              >
                Connect
              </button>
            </div>
          ))}
        </div>
        {currentConnections.length > MAX_CONNECTIONS && (
          <button className="dashboard-view-all-btn">View All</button>
        )}
      </div>

      {/* Community Stats */}
      <div className="dashboard-activity-section">
        <h4 className="dashboard-activity-header">Community Stats</h4>
        <div className="dashboard-community-stats">
          <div className="dashboard-stat-item">
            <div className="dashboard-stat-icon">📖</div>
            <div className="dashboard-stat-info">
              <span className="dashboard-stat-number">1,247</span>
              <span className="dashboard-stat-label">Stories Shared</span>
            </div>
          </div>
          <div className="dashboard-stat-item">
            <div className="dashboard-stat-icon">👥</div>
            <div className="dashboard-stat-info">
              <span className="dashboard-stat-number">3,891</span>
              <span className="dashboard-stat-label">Active Members</span>
            </div>
          </div>
          <div className="dashboard-stat-item">
            <div className="dashboard-stat-icon">❤️</div>
            <div className="dashboard-stat-info">
              <span className="dashboard-stat-number">15.2K</span>
              <span className="dashboard-stat-label">Hearts Given</span>
            </div>
          </div>
        </div>
      </div>

      {/* Trending Topics */}
      <div className="dashboard-activity-section">
        <h4 className="dashboard-activity-header">Trending Topics</h4>
        <div className="dashboard-trending-topics">
          <div className="dashboard-trending-item">
            <span className="dashboard-trending-tag">#BlackExcellence</span>
            <span className="dashboard-trending-count">24</span>
          </div>
          <div className="dashboard-trending-item">
            <span className="dashboard-trending-tag">#Inspiration</span>
            <span className="dashboard-trending-count">18</span>
          </div>
          <div className="dashboard-trending-item">
            <span className="dashboard-trending-tag">#Community</span>
            <span className="dashboard-trending-count">15</span>
          </div>
          <div className="dashboard-trending-item">
            <span className="dashboard-trending-tag">#Education</span>
            <span className="dashboard-trending-count">12</span>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default function DashboardLayout({ 
  children, 
  user, 
  userStories, 
  stories = [],
  userProfiles = {},
  onProfileUpdate, 
  onShowFullProfile,
  onCreatePost,
  onReaction,
  onShare,
  onBookmark,
  onStoryClick,
  notifications = [],
  recentActivity = [],
  suggestedConnections = [],
  onNotificationClick,
  onActivityClick,
  onConnectionClick,
  onQuickAction
}) {
  // Real-time activity updates (sample implementation)
  const [activityUpdates, setActivityUpdates] = React.useState(0);
  
  React.useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setActivityUpdates(prev => prev + 1);
    }, 30000); // Update every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const getTimeAgo = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now - time) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="dashboard-layout-root">
      <div className="dashboard-layout-grid">
        <ProfileSidebar 
          user={user} 
          userStories={userStories} 
          onProfileUpdate={onProfileUpdate}
          onShowFullProfile={onShowFullProfile}
        />
        <main className="dashboard-main">
          <StoriesRow 
            featuredStories={stories.slice(0, 4)}
            userProfiles={userProfiles}
            onStoryClick={onStoryClick || onCreatePost}
          />
          <PostInput onCreatePost={onCreatePost} user={user} />
          <Feed 
            stories={stories} 
            userProfiles={userProfiles}
            onReaction={onReaction}
            onShare={onShare}
            onBookmark={onBookmark}
            user={user}
            onCreatePost={onCreatePost}
          />
          {/* You can render children here if needed */}
          {children}
        </main>
        <RecentActivitySidebar 
          notifications={notifications}
          recentActivity={recentActivity}
          suggestedConnections={suggestedConnections}
          onNotificationClick={onNotificationClick}
          onActivityClick={onActivityClick}
          onConnectionClick={onConnectionClick}
          onQuickAction={onQuickAction || onCreatePost}
          user={user}
        />
      </div>
    </div>
  );
}
